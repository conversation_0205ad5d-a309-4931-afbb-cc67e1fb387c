<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>调试页面</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #f8fbff 0%, #e8f4f8 50%, #f0f8f5 100%);
            min-height: 100vh;
            padding: 2rem;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.9);
            border-radius: 20px;
            padding: 2rem;
        }
        
        .btn {
            background: linear-gradient(135deg, #F5A623 0%, #ff8c42 100%);
            color: white;
            border: none;
            padding: 1rem 2rem;
            font-size: 1.1rem;
            border-radius: 50px;
            cursor: pointer;
            margin: 1rem;
            transition: all 0.3s ease;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(245, 166, 35, 0.3);
        }
        
        .page {
            display: none;
        }
        
        .page.active {
            display: block;
        }
        
        .result {
            margin: 1rem 0;
            padding: 1rem;
            background: rgba(179, 205, 224, 0.1);
            border-radius: 8px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>法弈系统调试页面</h1>
        
        <!-- 第一页 -->
        <div id="page1" class="page active">
            <h2>欢迎页面</h2>
            <button id="btn1" class="btn">切换到第二页</button>
            <div id="result1" class="result"></div>
        </div>
        
        <!-- 第二页 -->
        <div id="page2" class="page">
            <h2>体验页面</h2>
            <button id="btn2" class="btn">填充示例数据</button>
            <button id="btn3" class="btn">开始分析</button>
            <textarea id="textarea1" placeholder="案情描述" style="width: 100%; height: 100px; margin: 1rem 0;"></textarea>
            <div id="result2" class="result"></div>
        </div>
    </div>

    <script>
        console.log('调试页面加载开始');
        
        // 等待DOM加载完成
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM加载完成，开始初始化');
            
            // 获取元素
            const btn1 = document.getElementById('btn1');
            const btn2 = document.getElementById('btn2');
            const btn3 = document.getElementById('btn3');
            const page1 = document.getElementById('page1');
            const page2 = document.getElementById('page2');
            const result1 = document.getElementById('result1');
            const result2 = document.getElementById('result2');
            const textarea1 = document.getElementById('textarea1');
            
            console.log('元素获取完成:', {btn1, btn2, btn3, page1, page2});
            
            // 按钮1：页面切换
            if (btn1) {
                btn1.addEventListener('click', function() {
                    console.log('点击了按钮1');
                    page1.classList.remove('active');
                    page2.classList.add('active');
                    result1.innerHTML = '页面切换成功！时间：' + new Date().toLocaleString();
                });
                console.log('按钮1事件绑定成功');
            }
            
            // 按钮2：填充数据
            if (btn2) {
                btn2.addEventListener('click', function() {
                    console.log('点击了按钮2');
                    const sampleText = '这是一个示例案情描述，用于测试打字机效果...';
                    typeWriter(textarea1, sampleText, 50);
                    result2.innerHTML = '开始填充示例数据...';
                });
                console.log('按钮2事件绑定成功');
            }
            
            // 按钮3：开始分析
            if (btn3) {
                btn3.addEventListener('click', function() {
                    console.log('点击了按钮3');
                    result2.innerHTML = '分析功能触发！文本内容：' + textarea1.value;
                });
                console.log('按钮3事件绑定成功');
            }
            
            console.log('所有事件绑定完成');
        });
        
        // 打字机效果
        function typeWriter(element, text, speed) {
            if (!element || !text) return;
            
            element.value = '';
            let i = 0;
            
            function type() {
                if (i < text.length) {
                    element.value += text.charAt(i);
                    i++;
                    setTimeout(type, speed);
                }
            }
            
            type();
        }
        
        console.log('脚本加载完成');
    </script>
</body>
</html>
