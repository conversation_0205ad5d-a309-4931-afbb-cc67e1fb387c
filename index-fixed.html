<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>法弈 - 智能法律诉讼辅助系统</title>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;700&display=swap" rel="stylesheet">
    <style>
        /* 全局样式 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Noto Sans SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            background: linear-gradient(135deg, #f8fbff 0%, #e8f4f8 50%, #f0f8f5 100%);
            min-height: 100vh;
            color: #333333;
            overflow-x: hidden;
        }

        /* 添加背景装饰 */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: 
                radial-gradient(circle at 20% 20%, rgba(179, 205, 224, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 80%, rgba(162, 217, 206, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 40% 60%, rgba(245, 166, 35, 0.05) 0%, transparent 50%);
            pointer-events: none;
            z-index: -1;
        }

        /* 页面切换 */
        .page {
            display: none;
            min-height: 100vh;
            animation: fadeIn 0.8s ease;
        }

        .page.active {
            display: block;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        /* 欢迎页样式 */
        .welcome-container {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            padding: 2rem;
            text-align: center;
        }

        .logo-text {
            font-size: 4rem;
            font-weight: 700;
            background: linear-gradient(135deg, #B3CDE0 0%, #A2D9CE 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 1rem;
        }

        .tagline {
            font-size: 1.2rem;
            color: #666;
            font-weight: 300;
            letter-spacing: 2px;
            margin-bottom: 4rem;
        }

        /* 功能网格 */
        .features-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 2rem;
            max-width: 800px;
            margin-bottom: 4rem;
        }

        .feature-card {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 16px;
            padding: 2rem;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .feature-card:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow: 0 20px 40px rgba(179, 205, 224, 0.3);
        }

        .feature-icon {
            width: 64px;
            height: 64px;
            margin: 0 auto 1.5rem;
            color: #B3CDE0;
            transition: all 0.3s ease;
        }

        .feature-card:hover .feature-icon {
            color: #F5A623;
            transform: scale(1.1) rotate(5deg);
        }

        .feature-title {
            font-size: 1.3rem;
            font-weight: 500;
            margin-bottom: 0.5rem;
            color: #333;
        }

        .feature-desc {
            font-size: 0.9rem;
            color: #666;
            line-height: 1.6;
        }

        /* 按钮样式 */
        .start-btn {
            background: linear-gradient(135deg, #F5A623 0%, #ff8c42 100%);
            color: white;
            border: none;
            padding: 1rem 3rem;
            font-size: 1.2rem;
            font-weight: 500;
            border-radius: 50px;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 8px 25px rgba(245, 166, 35, 0.3);
        }

        .start-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 35px rgba(245, 166, 35, 0.4);
        }

        /* 体验页样式 */
        .experience-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
            min-height: 100vh;
        }

        .section-title {
            text-align: center;
            font-size: 2rem;
            font-weight: 500;
            color: #333;
            margin-bottom: 3rem;
        }

        .input-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 2rem;
            margin-bottom: 2rem;
        }

        .input-group {
            position: relative;
        }

        .input-group label {
            display: block;
            font-weight: 500;
            color: #333;
            margin-bottom: 0.5rem;
        }

        .input-group textarea {
            width: 100%;
            min-height: 150px;
            padding: 1rem;
            border: 2px solid #e0e0e0;
            border-radius: 12px;
            font-family: inherit;
            font-size: 1rem;
            line-height: 1.6;
            resize: vertical;
            transition: all 0.3s ease;
            background: rgba(255, 255, 255, 0.9);
        }

        .input-group textarea:focus {
            outline: none;
            border-color: #B3CDE0;
            box-shadow: 0 0 0 3px rgba(179, 205, 224, 0.1);
        }

        .example-btn {
            position: absolute;
            top: 0;
            right: 0;
            background: #A2D9CE;
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 0 12px 0 12px;
            font-size: 0.8rem;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .example-btn:hover {
            background: #8cc7ba;
            transform: scale(1.05);
        }

        .analyze-section {
            text-align: center;
        }

        .analyze-btn {
            background: linear-gradient(135deg, #B3CDE0 0%, #A2D9CE 100%);
            color: white;
            border: none;
            padding: 1rem 2.5rem;
            font-size: 1.1rem;
            font-weight: 500;
            border-radius: 50px;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 8px 25px rgba(179, 205, 224, 0.3);
        }

        .analyze-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 35px rgba(179, 205, 224, 0.4);
        }

        /* 调试面板 */
        .debug-panel {
            position: fixed;
            bottom: 10px;
            right: 10px;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 10px;
            border-radius: 5px;
            font-size: 12px;
            max-width: 300px;
            max-height: 200px;
            overflow-y: auto;
            z-index: 9999;
        }

        .debug-panel button {
            margin-top: 5px;
            padding: 5px 10px;
            background: #333;
            color: white;
            border: none;
            border-radius: 3px;
            cursor: pointer;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .features-grid {
                grid-template-columns: 1fr;
                gap: 1.5rem;
            }
            
            .input-grid {
                grid-template-columns: 1fr;
                gap: 1.5rem;
            }
            
            .logo-text {
                font-size: 3rem;
            }
        }
    </style>
</head>
<body>
    <!-- 欢迎首页 -->
    <div id="welcomePage" class="page active">
        <div class="welcome-container">
            <!-- 顶部Logo和标语 -->
            <header class="welcome-header">
                <div class="logo">
                    <h1 class="logo-text">法弈</h1>
                    <p class="tagline">洞见法律脉络，预见诉讼未来</p>
                </div>
            </header>

            <!-- 中央功能简介区 -->
            <main class="features-grid">
                <div class="feature-card">
                    <div class="feature-icon">
                        <svg viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <circle cx="32" cy="32" r="4" fill="currentColor"/>
                            <circle cx="16" cy="16" r="3" fill="currentColor"/>
                            <circle cx="48" cy="16" r="3" fill="currentColor"/>
                            <circle cx="16" cy="48" r="3" fill="currentColor"/>
                            <circle cx="48" cy="48" r="3" fill="currentColor"/>
                            <path d="M32 28L19 19M32 28L45 19M32 36L19 45M32 36L45 45" stroke="currentColor" stroke-width="2"/>
                        </svg>
                    </div>
                    <h3 class="feature-title">案件智能解构</h3>
                    <p class="feature-desc">深入剖析案情，提炼关键要素</p>
                </div>

                <div class="feature-card">
                    <div class="feature-icon">
                        <svg viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M32 8L40 16H24L32 8Z" fill="currentColor"/>
                            <rect x="20" y="16" width="24" height="32" rx="2" fill="currentColor" opacity="0.7"/>
                            <circle cx="16" cy="32" r="4" fill="currentColor"/>
                            <circle cx="48" cy="32" r="4" fill="currentColor"/>
                            <path d="M20 32H16M44 32H48" stroke="currentColor" stroke-width="2"/>
                        </svg>
                    </div>
                    <h3 class="feature-title">证据链诊断</h3>
                    <p class="feature-desc">梳理现有证据，预警潜在缺口</p>
                </div>

                <div class="feature-card">
                    <div class="feature-icon">
                        <svg viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <rect x="16" y="12" width="32" height="40" rx="2" fill="currentColor" opacity="0.7"/>
                            <path d="M24 20H40M24 28H40M24 36H36" stroke="white" stroke-width="2"/>
                            <path d="M32 8L36 12H28L32 8Z" fill="currentColor"/>
                        </svg>
                    </div>
                    <h3 class="feature-title">法律与案例指引</h3>
                    <p class="feature-desc">智能匹配法规，精准推荐相似判例</p>
                </div>

                <div class="feature-card">
                    <div class="feature-icon">
                        <svg viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <rect x="20" y="8" width="24" height="48" rx="2" fill="currentColor" opacity="0.7"/>
                            <path d="M28 16H36M28 24H36M28 32H36M28 40H32" stroke="white" stroke-width="2"/>
                            <path d="M16 20L20 16V24L16 20Z" fill="currentColor"/>
                        </svg>
                    </div>
                    <h3 class="feature-title">诉讼文书生成</h3>
                    <p class="feature-desc">一键生成专业、规范的法律文书初稿</p>
                </div>
            </main>

            <!-- 底部体验按钮 -->
            <footer class="welcome-footer">
                <button id="startExperienceBtn" class="start-btn">
                    <span>立即体验</span>
                </button>
            </footer>
        </div>
    </div>

    <!-- 核心体验页 -->
    <div id="experiencePage" class="page">
        <div class="experience-container">
            <!-- 信息输入区 -->
            <section class="input-section">
                <h2 class="section-title">请告诉我们您遇到的问题</h2>
                <div class="input-grid">
                    <div class="input-group">
                        <label for="caseDescription">案情基本情况描述</label>
                        <textarea id="caseDescription" placeholder="请在这里详细描述事情的经过，涉及的人物、时间、地点等…"></textarea>
                        <button class="example-btn" id="fillExampleBtn">一键填充示例</button>
                    </div>
                    <div class="input-group">
                        <label for="legalClaim">您的主要诉讼请求</label>
                        <textarea id="legalClaim" placeholder="例如：要求对方赔偿损失10万元、要求对方履行合同等…"></textarea>
                    </div>
                </div>
                <div class="analyze-section">
                    <button id="analyzeBtn" class="analyze-btn">
                        <span>立即分析</span>
                    </button>
                </div>
            </section>
        </div>
    </div>

    <!-- 调试面板 -->
    <div id="debugPanel" class="debug-panel">
        <div id="debugInfo">调试信息：<br></div>
        <button onclick="toggleDebug()">关闭</button>
    </div>

    <script>
        // 调试功能
        function log(message) {
            console.log(message);
            const debugInfo = document.getElementById('debugInfo');
            if (debugInfo) {
                debugInfo.innerHTML += new Date().toLocaleTimeString() + ': ' + message + '<br>';
                debugInfo.scrollTop = debugInfo.scrollHeight;
            }
        }
        
        function toggleDebug() {
            const panel = document.getElementById('debugPanel');
            panel.style.display = panel.style.display === 'none' ? 'block' : 'none';
        }

        // 页面切换函数
        function switchToExperiencePage() {
            log('开始切换到体验页面');
            const welcomePage = document.getElementById('welcomePage');
            const experiencePage = document.getElementById('experiencePage');
            
            if (!welcomePage || !experiencePage) {
                log('错误：找不到页面元素');
                return;
            }
            
            welcomePage.classList.remove('active');
            experiencePage.classList.add('active');
            log('页面切换完成');
        }

        // 填充示例数据
        function fillExampleData() {
            log('开始填充示例数据');
            const caseDescription = document.getElementById('caseDescription');
            const legalClaim = document.getElementById('legalClaim');
            
            if (!caseDescription || !legalClaim) {
                log('错误：找不到输入框');
                return;
            }
            
            const sampleCase = "我真是气死了！今年7月15号，我在他们那个官方网站上花了一万两千多（具体是12888块）买了台叫"幻影X Pro"的笔记本，订单号是一串数字880123456789。18号电脑就到了，本来还挺开心的。结果好家伙，用了还不到一个月，从8月10号开始吧，这电脑就老是无缘无故蓝屏，然后卡死不动，机身烫得跟个暖手宝似的，我的好多工作文件都因为这个搞丢了！";
            const sampleClaim = "我就一个要求，把我买电脑的12888块钱全退给我！还有，因为这破电脑我好几天班都没上好，他们至少得赔我2000块钱的误工费！";
            
            // 打字机效果
            typeWriter(caseDescription, sampleCase, 30);
            setTimeout(() => {
                typeWriter(legalClaim, sampleClaim, 30);
            }, 1000);
        }

        // 打字机效果
        function typeWriter(element, text, speed) {
            element.value = '';
            let i = 0;
            
            function type() {
                if (i < text.length) {
                    element.value += text.charAt(i);
                    i++;
                    setTimeout(type, speed);
                }
            }
            
            type();
        }

        // 分析功能
        function startAnalysis() {
            log('开始分析');
            const caseDescription = document.getElementById('caseDescription').value;
            const legalClaim = document.getElementById('legalClaim').value;
            
            if (!caseDescription.trim() || !legalClaim.trim()) {
                alert('请填写案情描述和诉讼请求');
                return;
            }
            
            alert('分析功能已触发！\n案情描述长度：' + caseDescription.length + '\n诉讼请求长度：' + legalClaim.length);
            log('分析完成');
        }

        // 等待DOM加载完成
        document.addEventListener('DOMContentLoaded', function() {
            log('DOM加载完成，开始初始化');
            
            // 绑定事件
            const startBtn = document.getElementById('startExperienceBtn');
            const fillBtn = document.getElementById('fillExampleBtn');
            const analyzeBtn = document.getElementById('analyzeBtn');
            
            if (startBtn) {
                startBtn.addEventListener('click', switchToExperiencePage);
                log('立即体验按钮事件绑定成功');
            } else {
                log('错误：找不到立即体验按钮');
            }
            
            if (fillBtn) {
                fillBtn.addEventListener('click', fillExampleData);
                log('填充示例按钮事件绑定成功');
            }
            
            if (analyzeBtn) {
                analyzeBtn.addEventListener('click', startAnalysis);
                log('立即分析按钮事件绑定成功');
            }
            
            log('初始化完成');
        });

        log('脚本加载完成');
    </script>
</body>
</html>
