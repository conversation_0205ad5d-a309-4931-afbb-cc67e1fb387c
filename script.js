// 全局变量
let sampleData = null;
let networkAnimation = null;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    console.log('页面DOM加载完成');
    console.log('开始初始化...');

    // 先初始化事件监听器，这样用户可以立即交互
    initializeEventListeners();

    // 然后加载数据和初始化画布
    loadSampleData();
    initializeNetworkCanvas();

    console.log('初始化完成');
});

// 加载示例数据
async function loadSampleData() {
    try {
        const response = await fetch('./示例.json');
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        sampleData = await response.json();
        console.log('示例数据加载成功', sampleData);
    } catch (error) {
        console.error('加载示例数据失败:', error);
        // 如果加载失败，使用内置的示例数据
        sampleData = {
            userInput: {
                D_text: "我真是气死了！今年7月15号，我在他们那个官方网站上花了一万两千多（具体是12888块）买了台叫"幻影X Pro"的笔记本，订单号是一串数字880123456789。18号电脑就到了，本来还挺开心的。结果好家伙，用了还不到一个月，从8月10号开始吧，这电脑就老是无缘无故蓝屏，然后卡死不动，机身烫得跟个暖手宝似的，我的好多工作文件都因为这个搞丢了！我找他们客服理论，客服就一直踢皮球，翻来覆去就说是我自己装了什么不兼容的软件，还让我上传日志、拍视频，折腾了好半天，最后就说只给修，不给退不给换，修还要一个月！这不明摆着是电脑本身就有毛病吗？我坚决不同意维修，我就要退货退款！这事儿太耽误我工作了！",
                C_claim: "我就一个要求，把我买电脑的12888块钱全退给我！还有，因为这破电脑我好几天班都没上好，他们至少得赔我2000块钱的误工费！"
            },
            systemOutput: {
                R_analysis: {
                    evidenceAnalysis: {
                        evidenceGraph: {
                            nodes: [
                                { id: "node1", label: "建立买卖关系", type: "conclusion" },
                                { id: "node2", label: "网络购物订单", type: "evidence_owned" },
                                { id: "node3", label: "在线支付凭证", type: "evidence_owned" },
                                { id: "node4", label: "产品存在质量缺陷", type: "conclusion" },
                                { id: "node5", label: "故障现象的录像", type: "evidence_owned" },
                                { id: "node6", label: "与客服沟通记录", type: "evidence_owned" },
                                { id: "node7", label: "第三方质检报告", type: "evidence_gap" },
                                { id: "node8", label: "被告拒绝履行三包", type: "conclusion" },
                                { id: "node9", label: "支持退款退赔诉求", type: "final_claim" }
                            ]
                        }
                    },
                    legalInsights: {
                        relevantStatutes: [
                            {
                                statuteName: "《中华人民共和国消费者权益保护法》第二十四条",
                                content: "经营者提供的商品或者服务不符合质量要求的，消费者可以依照国家规定、当事人约定退货..."
                            }
                        ],
                        guidingCases: [
                            {
                                caseTitle: "王某诉某电子品牌商产品责任纠纷案",
                                caseNumber: "（2024）沪01民终XXXX号",
                                judgmentSummary: "法院认为，消费者购买的产品在"三包"期内出现非人为性能故障..."
                            }
                        ]
                    },
                    courtSimulation: {
                        adversarialSummary: {
                            ourArguments: [
                                "产品在三包期内出现严重性能故障，符合法定退货条件。",
                                "被告以不实理由（如软件不兼容）推卸责任，属于恶意违约。"
                            ],
                            opponentDefenses: [
                                "故障可能是由原告自行安装的第三方软件或病毒导致。",
                                "原告未能提供权威的检测报告证明产品存在固有缺陷。"
                            ]
                        },
                        strategyOptimization: [
                            "立即将设备送至具有司法鉴定资质的第三方检测机构进行检测。",
                            "整理并公证与客服的完整沟通记录。"
                        ]
                    }
                },
                L_document: {
                    title: "民事起诉状",
                    plaintiff: {
                        label: "原告",
                        name: "张三",
                        gender: "男",
                        birthDate: "1995年10月20日",
                        ethnicity: "汉族",
                        idNumber: "11010119951020XXXX",
                        address: "北京市朝阳区建国路XX号院X号楼X单元XXX室",
                        phone: "138-0013-8000"
                    },
                    defendant: {
                        label: "被告",
                        name: "未来科技电脑有限公司",
                        creditCode: "91440300MA5GXXXXXX",
                        address: "广东省深圳市南山区科技园路XX号",
                        legalRepresentative: "李四",
                        representativeTitle: "法定代表人",
                        phone: "0755-88886666"
                    },
                    caseCause: "买卖合同纠纷",
                    claims: {
                        title: "诉讼请求",
                        items: [
                            "一、判令被告立即退还原告购物款人民币12,888元；",
                            "二、判令被告赔偿原告因处理本事件导致的误工损失人民币2,000元；",
                            "三、判令被告承担本案全部诉讼费用。"
                        ]
                    }
                }
            }
        };
    }
}

// 初始化事件监听器
function initializeEventListeners() {
    console.log('初始化事件监听器...');
    if (typeof addDebugInfo === 'function') {
        addDebugInfo('开始初始化事件监听器');
    }

    // 开始体验按钮
    const startBtn = document.getElementById('startExperienceBtn');
    if (startBtn) {
        startBtn.addEventListener('click', function(e) {
            console.log('点击了开始体验按钮');
            if (typeof addDebugInfo === 'function') {
                addDebugInfo('点击了开始体验按钮');
            }
            e.preventDefault();
            switchToExperiencePage();
        });
        console.log('开始体验按钮事件已绑定');
        if (typeof addDebugInfo === 'function') {
            addDebugInfo('开始体验按钮事件已绑定');
        }
    } else {
        console.error('找不到开始体验按钮');
        if (typeof addDebugInfo === 'function') {
            addDebugInfo('错误：找不到开始体验按钮');
        }
    }

    // 功能卡片悬浮效果
    const featureCards = document.querySelectorAll('.feature-card');
    console.log('找到功能卡片数量:', featureCards.length);
    featureCards.forEach(card => {
        card.addEventListener('mouseenter', () => {
            card.style.transform = 'translateY(-8px) scale(1.02)';
        });
        card.addEventListener('mouseleave', () => {
            card.style.transform = 'translateY(0) scale(1)';
        });
    });

    // 一键填充示例按钮
    const fillExampleBtn = document.getElementById('fillExampleBtn');
    if (fillExampleBtn) {
        fillExampleBtn.addEventListener('click', function(e) {
            console.log('点击了填充示例按钮');
            e.preventDefault();
            fillExampleData();
        });
        console.log('填充示例按钮事件已绑定');
    }

    // 立即分析按钮
    const analyzeBtn = document.getElementById('analyzeBtn');
    if (analyzeBtn) {
        analyzeBtn.addEventListener('click', function(e) {
            console.log('点击了立即分析按钮');
            e.preventDefault();
            startAnalysis();
        });
        console.log('立即分析按钮事件已绑定');
    }

    // 查看报告按钮
    const viewReportBtn = document.getElementById('viewReportBtn');
    if (viewReportBtn) {
        viewReportBtn.addEventListener('click', function(e) {
            console.log('点击了查看报告按钮');
            e.preventDefault();
            showEvidenceChain();
        });
    }

    // 查看完整报告按钮
    const viewFullReportBtn = document.getElementById('viewFullReportBtn');
    if (viewFullReportBtn) {
        viewFullReportBtn.addEventListener('click', function(e) {
            console.log('点击了查看完整报告按钮');
            e.preventDefault();
            showFullReport();
        });
    }

    // 标签页切换
    const tabBtns = document.querySelectorAll('.tab-btn');
    tabBtns.forEach(btn => {
        btn.addEventListener('click', function(e) {
            console.log('点击了标签页:', btn.dataset.tab);
            e.preventDefault();
            switchTab(btn.dataset.tab);
        });
    });

    console.log('所有事件监听器初始化完成');
}

// 切换到体验页面
function switchToExperiencePage() {
    console.log('开始切换到体验页面');
    const welcomePage = document.getElementById('welcomePage');
    const experiencePage = document.getElementById('experiencePage');

    if (!welcomePage || !experiencePage) {
        console.error('找不到页面元素');
        return;
    }

    console.log('找到页面元素，开始切换动画');

    // 添加淡出动画
    welcomePage.style.transition = 'all 0.8s cubic-bezier(0.4, 0, 0.2, 1)';
    welcomePage.style.transform = 'translateY(-30px)';
    welcomePage.style.opacity = '0';

    setTimeout(() => {
        welcomePage.classList.remove('active');
        experiencePage.classList.add('active');
        console.log('页面切换完成');
    }, 400);
}

// 填充示例数据
function fillExampleData() {
    console.log('开始填充示例数据', sampleData);
    if (!sampleData) {
        console.error('示例数据未加载');
        return;
    }

    const caseDescription = document.getElementById('caseDescription');
    const legalClaim = document.getElementById('legalClaim');

    if (!caseDescription || !legalClaim) {
        console.error('找不到输入框元素');
        return;
    }

    console.log('开始填充数据...');

    // 添加打字机效果
    typeWriter(caseDescription, sampleData.userInput.D_text, 30);
    setTimeout(() => {
        typeWriter(legalClaim, sampleData.userInput.C_claim, 30);
    }, 1000);
}

// 打字机效果
function typeWriter(element, text, speed) {
    if (!element || !text) {
        console.error('打字机效果参数错误');
        return;
    }

    element.value = '';
    let i = 0;

    function type() {
        if (i < text.length) {
            element.value += text.charAt(i);
            i++;
            setTimeout(type, speed);
        } else {
            console.log('打字机效果完成');
        }
    }

    console.log('开始打字机效果，文本长度:', text.length);
    type();
}

// 开始分析
function startAnalysis() {
    console.log('开始分析');
    const caseDescription = document.getElementById('caseDescription');
    const legalClaim = document.getElementById('legalClaim');

    if (!caseDescription || !legalClaim) {
        console.error('找不到输入框');
        return;
    }

    const caseText = caseDescription.value;
    const claimText = legalClaim.value;

    if (!caseText.trim() || !claimText.trim()) {
        alert('请填写案情描述和诉讼请求');
        return;
    }

    console.log('输入验证通过，开始显示分析区域');

    // 显示分析区域
    const analysisSection = document.getElementById('analysisSection');
    if (analysisSection) {
        analysisSection.classList.remove('hidden');
        console.log('分析区域已显示');

        // 滚动到分析区域
        setTimeout(() => {
            analysisSection.scrollIntoView({ behavior: 'smooth' });
        }, 100);

        // 开始网络动画
        startNetworkAnimation();

        // 3秒后显示查看报告按钮
        setTimeout(() => {
            const viewReportBtn = document.getElementById('viewReportBtn');
            if (viewReportBtn) {
                viewReportBtn.classList.remove('hidden');
                viewReportBtn.style.animation = 'fadeIn 0.5s ease';
                console.log('查看报告按钮已显示');
            }
        }, 3000);
    } else {
        console.error('找不到分析区域');
    }
}

// 初始化网络画布
function initializeNetworkCanvas() {
    const canvas = document.getElementById('networkCanvas');
    if (!canvas) {
        console.error('找不到网络画布');
        return;
    }

    const ctx = canvas.getContext('2d');
    if (!ctx) {
        console.error('无法获取画布上下文');
        return;
    }

    // 设置画布尺寸
    function resizeCanvas() {
        const rect = canvas.parentElement.getBoundingClientRect();
        canvas.width = rect.width || 800;
        canvas.height = rect.height || 400;
        console.log('画布尺寸:', canvas.width, 'x', canvas.height);
    }

    resizeCanvas();
    window.addEventListener('resize', resizeCanvas);
    console.log('网络画布初始化完成');
}

// 开始网络动画
function startNetworkAnimation() {
    const canvas = document.getElementById('networkCanvas');
    const ctx = canvas.getContext('2d');
    
    // 节点和连接
    const nodes = [];
    const connections = [];
    const keywords = ['借条', '转账记录', '违约', '张三', '合同', '证据'];
    
    // 创建节点
    for (let i = 0; i < 8; i++) {
        nodes.push({
            x: Math.random() * canvas.width,
            y: Math.random() * canvas.height,
            vx: (Math.random() - 0.5) * 2,
            vy: (Math.random() - 0.5) * 2,
            radius: Math.random() * 5 + 3,
            keyword: keywords[Math.floor(Math.random() * keywords.length)],
            alpha: Math.random()
        });
    }
    
    // 创建连接
    for (let i = 0; i < nodes.length; i++) {
        for (let j = i + 1; j < nodes.length; j++) {
            if (Math.random() < 0.3) {
                connections.push({ from: i, to: j, alpha: 0 });
            }
        }
    }
    
    function animate() {
        ctx.clearRect(0, 0, canvas.width, canvas.height);
        
        // 更新和绘制连接
        connections.forEach(conn => {
            const fromNode = nodes[conn.from];
            const toNode = nodes[conn.to];
            
            conn.alpha = Math.min(conn.alpha + 0.01, 0.3);
            
            ctx.strokeStyle = `rgba(179, 205, 224, ${conn.alpha})`;
            ctx.lineWidth = 1;
            ctx.beginPath();
            ctx.moveTo(fromNode.x, fromNode.y);
            ctx.lineTo(toNode.x, toNode.y);
            ctx.stroke();
        });
        
        // 更新和绘制节点
        nodes.forEach(node => {
            // 更新位置
            node.x += node.vx;
            node.y += node.vy;
            
            // 边界反弹
            if (node.x < 0 || node.x > canvas.width) node.vx *= -1;
            if (node.y < 0 || node.y > canvas.height) node.vy *= -1;
            
            // 更新透明度
            node.alpha = Math.sin(Date.now() * 0.003 + node.x * 0.01) * 0.3 + 0.7;
            
            // 绘制节点
            ctx.fillStyle = `rgba(179, 205, 224, ${node.alpha})`;
            ctx.beginPath();
            ctx.arc(node.x, node.y, node.radius, 0, Math.PI * 2);
            ctx.fill();
            
            // 绘制关键词
            ctx.fillStyle = `rgba(51, 51, 51, ${node.alpha * 0.8})`;
            ctx.font = '12px Noto Sans SC';
            ctx.textAlign = 'center';
            ctx.fillText(node.keyword, node.x, node.y - node.radius - 5);
        });
        
        networkAnimation = requestAnimationFrame(animate);
    }
    
    animate();
}

// 显示证据链
function showEvidenceChain() {
    // 停止网络动画
    if (networkAnimation) {
        cancelAnimationFrame(networkAnimation);
    }
    
    // 显示结果区域
    const resultsSection = document.getElementById('resultsSection');
    resultsSection.classList.remove('hidden');
    
    // 滚动到结果区域
    resultsSection.scrollIntoView({ behavior: 'smooth' });
    
    // 生成证据链节点
    generateEvidenceNodes();
}

// 生成证据链节点
function generateEvidenceNodes() {
    if (!sampleData || !sampleData.systemOutput) return;
    
    const timeline = document.querySelector('.evidence-timeline');
    timeline.innerHTML = '';
    
    const evidenceData = sampleData.systemOutput.R_analysis.evidenceAnalysis.evidenceGraph.nodes;
    
    evidenceData.forEach((node, index) => {
        const nodeElement = document.createElement('div');
        nodeElement.className = `evidence-node ${node.type === 'evidence_gap' ? 'gap' : ''}`;
        nodeElement.style.animationDelay = `${index * 0.2}s`;
        
        nodeElement.innerHTML = `
            <h4>${node.label}</h4>
            <p>${getNodeDescription(node)}</p>
        `;
        
        timeline.appendChild(nodeElement);
    });
}

// 获取节点描述
function getNodeDescription(node) {
    const descriptions = {
        'evidence_owned': '已有证据，可以支持相关主张',
        'evidence_gap': '证据缺失，需要补充相关材料',
        'conclusion': '基于现有证据得出的结论',
        'final_claim': '最终的诉讼请求'
    };
    
    return descriptions[node.type] || '相关证据材料';
}

// 显示完整报告
function showFullReport() {
    const fullReport = document.getElementById('fullReport');
    fullReport.classList.remove('hidden');
    
    // 滚动到完整报告
    fullReport.scrollIntoView({ behavior: 'smooth' });
    
    // 加载报告内容
    loadReportContent();
}

// 加载报告内容
function loadReportContent() {
    if (!sampleData || !sampleData.systemOutput) return;
    
    const data = sampleData.systemOutput;
    
    // 法律洞察内容
    const legalInsights = document.getElementById('legal-insights');
    legalInsights.innerHTML = generateLegalInsightsHTML(data.R_analysis.legalInsights);
    
    // 模拟法庭内容
    const courtSimulation = document.getElementById('court-simulation');
    courtSimulation.innerHTML = generateCourtSimulationHTML(data.R_analysis.courtSimulation);
    
    // 法律文书内容
    const legalDocument = document.getElementById('legal-document');
    legalDocument.innerHTML = generateLegalDocumentHTML(data.L_document);
}

// 生成法律洞察HTML
function generateLegalInsightsHTML(insights) {
    let html = '<div class="insights-content">';
    
    html += '<h4>相关法律条文</h4>';
    insights.relevantStatutes.forEach(statute => {
        html += `
            <div class="statute-item">
                <h5>${statute.statuteName}</h5>
                <p>${statute.content}</p>
            </div>
        `;
    });
    
    html += '<h4>指导案例</h4>';
    insights.guidingCases.forEach(case_ => {
        html += `
            <div class="case-item">
                <h5>${case_.caseTitle}</h5>
                <p><strong>案号：</strong>${case_.caseNumber}</p>
                <p>${case_.judgmentSummary}</p>
            </div>
        `;
    });
    
    html += '</div>';
    return html;
}

// 生成模拟法庭HTML
function generateCourtSimulationHTML(simulation) {
    let html = '<div class="simulation-content">';
    
    html += '<h4>我方论点</h4><ul>';
    simulation.adversarialSummary.ourArguments.forEach(arg => {
        html += `<li>${arg}</li>`;
    });
    html += '</ul>';
    
    html += '<h4>对方可能的抗辩</h4><ul>';
    simulation.adversarialSummary.opponentDefenses.forEach(defense => {
        html += `<li>${defense}</li>`;
    });
    html += '</ul>';
    
    html += '<h4>策略建议</h4><ul>';
    simulation.strategyOptimization.forEach(strategy => {
        html += `<li>${strategy}</li>`;
    });
    html += '</ul>';
    
    html += '</div>';
    return html;
}

// 生成法律文书HTML
function generateLegalDocumentHTML(document) {
    return `
        <div class="document-content">
            <div class="document-header">
                <h3>${document.title}</h3>
            </div>
            <div class="document-body">
                <div class="party-info">
                    <p><strong>${document.plaintiff.label}：</strong>${document.plaintiff.name}，${document.plaintiff.gender}，${document.plaintiff.birthDate}生，${document.plaintiff.ethnicity}族</p>
                    <p>身份证号：${document.plaintiff.idNumber}</p>
                    <p>住址：${document.plaintiff.address}</p>
                    <p>联系电话：${document.plaintiff.phone}</p>
                </div>
                <div class="party-info">
                    <p><strong>${document.defendant.label}：</strong>${document.defendant.name}</p>
                    <p>统一社会信用代码：${document.defendant.creditCode}</p>
                    <p>住所地：${document.defendant.address}</p>
                    <p>${document.defendant.representativeTitle}：${document.defendant.legalRepresentative}</p>
                    <p>联系电话：${document.defendant.phone}</p>
                </div>
                <div class="case-cause">
                    <p><strong>案由：</strong>${document.caseCause}</p>
                </div>
                <div class="claims">
                    <h4>${document.claims.title}：</h4>
                    <ol>
                        ${document.claims.items.map(item => `<li>${item}</li>`).join('')}
                    </ol>
                </div>
            </div>
            <div class="download-section">
                <button class="download-btn" onclick="downloadDocument()">下载完整文书</button>
            </div>
        </div>
    `;
}

// 切换标签页
function switchTab(tabId) {
    // 移除所有活动状态
    document.querySelectorAll('.tab-btn').forEach(btn => btn.classList.remove('active'));
    document.querySelectorAll('.tab-panel').forEach(panel => panel.classList.remove('active'));
    
    // 添加活动状态
    document.querySelector(`[data-tab="${tabId}"]`).classList.add('active');
    document.getElementById(tabId).classList.add('active');
}

// 下载文档（模拟功能）
function downloadDocument() {
    alert('文档下载功能已触发！在实际应用中，这里会生成并下载PDF文件。');
}
