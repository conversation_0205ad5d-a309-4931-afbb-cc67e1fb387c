// 全局变量
let sampleData = null;
let networkAnimation = null;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    loadSampleData();
    initializeEventListeners();
    initializeNetworkCanvas();
});

// 加载示例数据
async function loadSampleData() {
    try {
        const response = await fetch('示例.json');
        sampleData = await response.json();
        console.log('示例数据加载成功', sampleData);
    } catch (error) {
        console.error('加载示例数据失败:', error);
        // 如果加载失败，使用内置的示例数据
        sampleData = {
            userInput: {
                D_text: "我真是气死了！今年7月15号，我在他们那个官方网站上花了一万两千多（具体是12888块）买了台叫"幻影X Pro"的笔记本...",
                C_claim: "我就一个要求，把我买电脑的12888块钱全退给我！还有，因为这破电脑我好几天班都没上好，他们至少得赔我2000块钱的误工费！"
            }
        };
    }
}

// 初始化事件监听器
function initializeEventListeners() {
    // 开始体验按钮
    const startBtn = document.getElementById('startExperienceBtn');
    startBtn.addEventListener('click', switchToExperiencePage);

    // 功能卡片悬浮效果
    const featureCards = document.querySelectorAll('.feature-card');
    featureCards.forEach(card => {
        card.addEventListener('mouseenter', () => {
            card.style.transform = 'translateY(-8px) scale(1.02)';
        });
        card.addEventListener('mouseleave', () => {
            card.style.transform = 'translateY(0) scale(1)';
        });
    });

    // 一键填充示例按钮
    const fillExampleBtn = document.getElementById('fillExampleBtn');
    fillExampleBtn.addEventListener('click', fillExampleData);

    // 立即分析按钮
    const analyzeBtn = document.getElementById('analyzeBtn');
    analyzeBtn.addEventListener('click', startAnalysis);

    // 查看报告按钮
    const viewReportBtn = document.getElementById('viewReportBtn');
    viewReportBtn.addEventListener('click', showEvidenceChain);

    // 查看完整报告按钮
    const viewFullReportBtn = document.getElementById('viewFullReportBtn');
    viewFullReportBtn.addEventListener('click', showFullReport);

    // 标签页切换
    const tabBtns = document.querySelectorAll('.tab-btn');
    tabBtns.forEach(btn => {
        btn.addEventListener('click', () => switchTab(btn.dataset.tab));
    });
}

// 切换到体验页面
function switchToExperiencePage() {
    const welcomePage = document.getElementById('welcomePage');
    const experiencePage = document.getElementById('experiencePage');
    
    // 添加淡出动画
    welcomePage.style.transform = 'translateY(-30px)';
    welcomePage.style.opacity = '0';
    
    setTimeout(() => {
        welcomePage.classList.remove('active');
        experiencePage.classList.add('active');
        
        // 添加淡入动画
        experiencePage.style.transform = 'translateY(0)';
        experiencePage.style.opacity = '1';
    }, 400);
}

// 填充示例数据
function fillExampleData() {
    if (!sampleData) return;
    
    const caseDescription = document.getElementById('caseDescription');
    const legalClaim = document.getElementById('legalClaim');
    
    // 添加打字机效果
    typeWriter(caseDescription, sampleData.userInput.D_text, 50);
    setTimeout(() => {
        typeWriter(legalClaim, sampleData.userInput.C_claim, 50);
    }, 1000);
}

// 打字机效果
function typeWriter(element, text, speed) {
    element.value = '';
    let i = 0;
    
    function type() {
        if (i < text.length) {
            element.value += text.charAt(i);
            i++;
            setTimeout(type, speed);
        }
    }
    
    type();
}

// 开始分析
function startAnalysis() {
    const caseDescription = document.getElementById('caseDescription').value;
    const legalClaim = document.getElementById('legalClaim').value;
    
    if (!caseDescription.trim() || !legalClaim.trim()) {
        alert('请填写案情描述和诉讼请求');
        return;
    }
    
    // 显示分析区域
    const analysisSection = document.getElementById('analysisSection');
    analysisSection.classList.remove('hidden');
    
    // 滚动到分析区域
    analysisSection.scrollIntoView({ behavior: 'smooth' });
    
    // 开始网络动画
    startNetworkAnimation();
    
    // 3秒后显示查看报告按钮
    setTimeout(() => {
        const viewReportBtn = document.getElementById('viewReportBtn');
        viewReportBtn.classList.remove('hidden');
        viewReportBtn.style.animation = 'fadeIn 0.5s ease';
    }, 3000);
}

// 初始化网络画布
function initializeNetworkCanvas() {
    const canvas = document.getElementById('networkCanvas');
    const ctx = canvas.getContext('2d');
    
    // 设置画布尺寸
    function resizeCanvas() {
        const rect = canvas.parentElement.getBoundingClientRect();
        canvas.width = rect.width;
        canvas.height = rect.height;
    }
    
    resizeCanvas();
    window.addEventListener('resize', resizeCanvas);
}

// 开始网络动画
function startNetworkAnimation() {
    const canvas = document.getElementById('networkCanvas');
    const ctx = canvas.getContext('2d');
    
    // 节点和连接
    const nodes = [];
    const connections = [];
    const keywords = ['借条', '转账记录', '违约', '张三', '合同', '证据'];
    
    // 创建节点
    for (let i = 0; i < 8; i++) {
        nodes.push({
            x: Math.random() * canvas.width,
            y: Math.random() * canvas.height,
            vx: (Math.random() - 0.5) * 2,
            vy: (Math.random() - 0.5) * 2,
            radius: Math.random() * 5 + 3,
            keyword: keywords[Math.floor(Math.random() * keywords.length)],
            alpha: Math.random()
        });
    }
    
    // 创建连接
    for (let i = 0; i < nodes.length; i++) {
        for (let j = i + 1; j < nodes.length; j++) {
            if (Math.random() < 0.3) {
                connections.push({ from: i, to: j, alpha: 0 });
            }
        }
    }
    
    function animate() {
        ctx.clearRect(0, 0, canvas.width, canvas.height);
        
        // 更新和绘制连接
        connections.forEach(conn => {
            const fromNode = nodes[conn.from];
            const toNode = nodes[conn.to];
            
            conn.alpha = Math.min(conn.alpha + 0.01, 0.3);
            
            ctx.strokeStyle = `rgba(179, 205, 224, ${conn.alpha})`;
            ctx.lineWidth = 1;
            ctx.beginPath();
            ctx.moveTo(fromNode.x, fromNode.y);
            ctx.lineTo(toNode.x, toNode.y);
            ctx.stroke();
        });
        
        // 更新和绘制节点
        nodes.forEach(node => {
            // 更新位置
            node.x += node.vx;
            node.y += node.vy;
            
            // 边界反弹
            if (node.x < 0 || node.x > canvas.width) node.vx *= -1;
            if (node.y < 0 || node.y > canvas.height) node.vy *= -1;
            
            // 更新透明度
            node.alpha = Math.sin(Date.now() * 0.003 + node.x * 0.01) * 0.3 + 0.7;
            
            // 绘制节点
            ctx.fillStyle = `rgba(179, 205, 224, ${node.alpha})`;
            ctx.beginPath();
            ctx.arc(node.x, node.y, node.radius, 0, Math.PI * 2);
            ctx.fill();
            
            // 绘制关键词
            ctx.fillStyle = `rgba(51, 51, 51, ${node.alpha * 0.8})`;
            ctx.font = '12px Noto Sans SC';
            ctx.textAlign = 'center';
            ctx.fillText(node.keyword, node.x, node.y - node.radius - 5);
        });
        
        networkAnimation = requestAnimationFrame(animate);
    }
    
    animate();
}

// 显示证据链
function showEvidenceChain() {
    // 停止网络动画
    if (networkAnimation) {
        cancelAnimationFrame(networkAnimation);
    }
    
    // 显示结果区域
    const resultsSection = document.getElementById('resultsSection');
    resultsSection.classList.remove('hidden');
    
    // 滚动到结果区域
    resultsSection.scrollIntoView({ behavior: 'smooth' });
    
    // 生成证据链节点
    generateEvidenceNodes();
}

// 生成证据链节点
function generateEvidenceNodes() {
    if (!sampleData || !sampleData.systemOutput) return;
    
    const timeline = document.querySelector('.evidence-timeline');
    timeline.innerHTML = '';
    
    const evidenceData = sampleData.systemOutput.R_analysis.evidenceAnalysis.evidenceGraph.nodes;
    
    evidenceData.forEach((node, index) => {
        const nodeElement = document.createElement('div');
        nodeElement.className = `evidence-node ${node.type === 'evidence_gap' ? 'gap' : ''}`;
        nodeElement.style.animationDelay = `${index * 0.2}s`;
        
        nodeElement.innerHTML = `
            <h4>${node.label}</h4>
            <p>${getNodeDescription(node)}</p>
        `;
        
        timeline.appendChild(nodeElement);
    });
}

// 获取节点描述
function getNodeDescription(node) {
    const descriptions = {
        'evidence_owned': '已有证据，可以支持相关主张',
        'evidence_gap': '证据缺失，需要补充相关材料',
        'conclusion': '基于现有证据得出的结论',
        'final_claim': '最终的诉讼请求'
    };
    
    return descriptions[node.type] || '相关证据材料';
}

// 显示完整报告
function showFullReport() {
    const fullReport = document.getElementById('fullReport');
    fullReport.classList.remove('hidden');
    
    // 滚动到完整报告
    fullReport.scrollIntoView({ behavior: 'smooth' });
    
    // 加载报告内容
    loadReportContent();
}

// 加载报告内容
function loadReportContent() {
    if (!sampleData || !sampleData.systemOutput) return;
    
    const data = sampleData.systemOutput;
    
    // 法律洞察内容
    const legalInsights = document.getElementById('legal-insights');
    legalInsights.innerHTML = generateLegalInsightsHTML(data.R_analysis.legalInsights);
    
    // 模拟法庭内容
    const courtSimulation = document.getElementById('court-simulation');
    courtSimulation.innerHTML = generateCourtSimulationHTML(data.R_analysis.courtSimulation);
    
    // 法律文书内容
    const legalDocument = document.getElementById('legal-document');
    legalDocument.innerHTML = generateLegalDocumentHTML(data.L_document);
}

// 生成法律洞察HTML
function generateLegalInsightsHTML(insights) {
    let html = '<div class="insights-content">';
    
    html += '<h4>相关法律条文</h4>';
    insights.relevantStatutes.forEach(statute => {
        html += `
            <div class="statute-item">
                <h5>${statute.statuteName}</h5>
                <p>${statute.content}</p>
            </div>
        `;
    });
    
    html += '<h4>指导案例</h4>';
    insights.guidingCases.forEach(case_ => {
        html += `
            <div class="case-item">
                <h5>${case_.caseTitle}</h5>
                <p><strong>案号：</strong>${case_.caseNumber}</p>
                <p>${case_.judgmentSummary}</p>
            </div>
        `;
    });
    
    html += '</div>';
    return html;
}

// 生成模拟法庭HTML
function generateCourtSimulationHTML(simulation) {
    let html = '<div class="simulation-content">';
    
    html += '<h4>我方论点</h4><ul>';
    simulation.adversarialSummary.ourArguments.forEach(arg => {
        html += `<li>${arg}</li>`;
    });
    html += '</ul>';
    
    html += '<h4>对方可能的抗辩</h4><ul>';
    simulation.adversarialSummary.opponentDefenses.forEach(defense => {
        html += `<li>${defense}</li>`;
    });
    html += '</ul>';
    
    html += '<h4>策略建议</h4><ul>';
    simulation.strategyOptimization.forEach(strategy => {
        html += `<li>${strategy}</li>`;
    });
    html += '</ul>';
    
    html += '</div>';
    return html;
}

// 生成法律文书HTML
function generateLegalDocumentHTML(document) {
    return `
        <div class="document-content">
            <div class="document-header">
                <h3>${document.title}</h3>
            </div>
            <div class="document-body">
                <div class="party-info">
                    <p><strong>${document.plaintiff.label}：</strong>${document.plaintiff.name}，${document.plaintiff.gender}，${document.plaintiff.birthDate}生，${document.plaintiff.ethnicity}族</p>
                    <p>身份证号：${document.plaintiff.idNumber}</p>
                    <p>住址：${document.plaintiff.address}</p>
                    <p>联系电话：${document.plaintiff.phone}</p>
                </div>
                <div class="party-info">
                    <p><strong>${document.defendant.label}：</strong>${document.defendant.name}</p>
                    <p>统一社会信用代码：${document.defendant.creditCode}</p>
                    <p>住所地：${document.defendant.address}</p>
                    <p>${document.defendant.representativeTitle}：${document.defendant.legalRepresentative}</p>
                    <p>联系电话：${document.defendant.phone}</p>
                </div>
                <div class="case-cause">
                    <p><strong>案由：</strong>${document.caseCause}</p>
                </div>
                <div class="claims">
                    <h4>${document.claims.title}：</h4>
                    <ol>
                        ${document.claims.items.map(item => `<li>${item}</li>`).join('')}
                    </ol>
                </div>
            </div>
            <div class="download-section">
                <button class="download-btn" onclick="downloadDocument()">下载完整文书</button>
            </div>
        </div>
    `;
}

// 切换标签页
function switchTab(tabId) {
    // 移除所有活动状态
    document.querySelectorAll('.tab-btn').forEach(btn => btn.classList.remove('active'));
    document.querySelectorAll('.tab-panel').forEach(panel => panel.classList.remove('active'));
    
    // 添加活动状态
    document.querySelector(`[data-tab="${tabId}"]`).classList.add('active');
    document.getElementById(tabId).classList.add('active');
}

// 下载文档（模拟功能）
function downloadDocument() {
    alert('文档下载功能已触发！在实际应用中，这里会生成并下载PDF文件。');
}
