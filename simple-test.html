<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简单测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #f8fbff 0%, #e8f4f8 50%, #f0f8f5 100%);
            min-height: 100vh;
            padding: 2rem;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            text-align: center;
        }
        
        .page {
            display: none;
            padding: 2rem;
            background: rgba(255, 255, 255, 0.9);
            border-radius: 20px;
            margin: 1rem 0;
        }
        
        .page.active {
            display: block;
        }
        
        .btn {
            background: linear-gradient(135deg, #F5A623 0%, #ff8c42 100%);
            color: white;
            border: none;
            padding: 1rem 2rem;
            font-size: 1.1rem;
            border-radius: 50px;
            cursor: pointer;
            margin: 1rem;
            transition: all 0.3s ease;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(245, 166, 35, 0.3);
        }
        
        .debug {
            position: fixed;
            top: 10px;
            right: 10px;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 10px;
            border-radius: 5px;
            font-size: 12px;
            max-width: 300px;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>法弈 - 简单测试版</h1>
        
        <!-- 第一页 -->
        <div id="page1" class="page active">
            <h2>欢迎页面</h2>
            <p>这是欢迎页面</p>
            <button id="testBtn" class="btn">立即体验</button>
        </div>
        
        <!-- 第二页 -->
        <div id="page2" class="page">
            <h2>体验页面</h2>
            <p>恭喜！页面切换成功！</p>
            <button id="backBtn" class="btn">返回首页</button>
        </div>
    </div>
    
    <!-- 调试信息 -->
    <div id="debug" class="debug">
        <div id="debugContent">调试信息：<br></div>
    </div>

    <script>
        // 调试函数
        function log(message) {
            console.log(message);
            const debugContent = document.getElementById('debugContent');
            if (debugContent) {
                debugContent.innerHTML += new Date().toLocaleTimeString() + ': ' + message + '<br>';
                debugContent.scrollTop = debugContent.scrollHeight;
            }
        }
        
        // 页面切换函数
        function switchPage(fromId, toId) {
            log('开始切换页面: ' + fromId + ' -> ' + toId);
            const fromPage = document.getElementById(fromId);
            const toPage = document.getElementById(toId);
            
            if (!fromPage || !toPage) {
                log('错误：找不到页面元素');
                return;
            }
            
            fromPage.classList.remove('active');
            toPage.classList.add('active');
            log('页面切换完成');
        }
        
        // 等待DOM加载
        document.addEventListener('DOMContentLoaded', function() {
            log('DOM加载完成');
            
            // 获取按钮
            const testBtn = document.getElementById('testBtn');
            const backBtn = document.getElementById('backBtn');
            
            log('按钮元素: testBtn=' + (testBtn ? '找到' : '未找到') + ', backBtn=' + (backBtn ? '找到' : '未找到'));
            
            // 绑定事件
            if (testBtn) {
                testBtn.addEventListener('click', function() {
                    log('点击了立即体验按钮');
                    switchPage('page1', 'page2');
                });
                log('立即体验按钮事件绑定成功');
            }
            
            if (backBtn) {
                backBtn.addEventListener('click', function() {
                    log('点击了返回按钮');
                    switchPage('page2', 'page1');
                });
                log('返回按钮事件绑定成功');
            }
            
            log('初始化完成');
        });
        
        // 测试点击事件
        function testClick() {
            log('测试点击函数被调用');
            alert('点击测试成功！');
        }
        
        log('脚本加载完成');
    </script>
</body>
</html>
