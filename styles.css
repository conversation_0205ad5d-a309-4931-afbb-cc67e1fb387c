/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Noto Sans SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    background: linear-gradient(135deg, #f8fbff 0%, #e8f4f8 50%, #f0f8f5 100%);
    min-height: 100vh;
    color: #333333;
    overflow-x: hidden;
}

/* 添加背景装饰 */
body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(circle at 20% 20%, rgba(179, 205, 224, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 80%, rgba(162, 217, 206, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 40% 60%, rgba(245, 166, 35, 0.05) 0%, transparent 50%);
    pointer-events: none;
    z-index: -1;
}

/* 页面切换 */
.page {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100vh;
    opacity: 0;
    visibility: hidden;
    transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
    transform: translateY(30px);
}

.page.active {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

/* 欢迎页样式 */
.welcome-container {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    padding: 2rem;
    text-align: center;
}

.welcome-header {
    margin-bottom: 4rem;
}

.logo-text {
    font-size: 4rem;
    font-weight: 700;
    background: linear-gradient(135deg, #B3CDE0 0%, #A2D9CE 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 1rem;
    animation: logoGlow 3s ease-in-out infinite alternate;
}

@keyframes logoGlow {
    0% { filter: drop-shadow(0 0 10px rgba(179, 205, 224, 0.3)); }
    100% { filter: drop-shadow(0 0 20px rgba(162, 217, 206, 0.5)); }
}

.tagline {
    font-size: 1.2rem;
    color: #666;
    font-weight: 300;
    letter-spacing: 2px;
}

/* 功能网格 */
.features-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 2rem;
    max-width: 800px;
    margin-bottom: 4rem;
}

.feature-card {
    background: rgba(255, 255, 255, 0.9);
    border-radius: 16px;
    padding: 2rem;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    position: relative;
    overflow: hidden;
}

.feature-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(179, 205, 224, 0.1), transparent);
    transition: left 0.5s;
}

.feature-card:hover::before {
    left: 100%;
}

.feature-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 20px 40px rgba(179, 205, 224, 0.3);
}

.feature-icon {
    width: 64px;
    height: 64px;
    margin: 0 auto 1.5rem;
    color: #B3CDE0;
    transition: all 0.3s ease;
}

.feature-card:hover .feature-icon {
    color: #F5A623;
    transform: scale(1.1) rotate(5deg);
}

.feature-title {
    font-size: 1.3rem;
    font-weight: 500;
    margin-bottom: 0.5rem;
    color: #333;
}

.feature-desc {
    font-size: 0.9rem;
    color: #666;
    line-height: 1.6;
}

/* 开始体验按钮 */
.start-btn {
    position: relative;
    background: linear-gradient(135deg, #F5A623 0%, #ff8c42 100%);
    color: white;
    border: none;
    padding: 1rem 3rem;
    font-size: 1.2rem;
    font-weight: 500;
    border-radius: 50px;
    cursor: pointer;
    transition: all 0.3s ease;
    overflow: hidden;
    box-shadow: 0 8px 25px rgba(245, 166, 35, 0.3);
}

.start-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 35px rgba(245, 166, 35, 0.4);
}

.btn-glow {
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.6s;
}

.start-btn:hover .btn-glow {
    left: 100%;
}

/* 核心体验页样式 */
.experience-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem;
    min-height: 100vh;
}

.section-title {
    text-align: center;
    font-size: 2rem;
    font-weight: 500;
    color: #333;
    margin-bottom: 3rem;
    position: relative;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background: linear-gradient(135deg, #B3CDE0, #A2D9CE);
    border-radius: 2px;
}

/* 输入区域 */
.input-section {
    margin-bottom: 4rem;
}

.input-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    margin-bottom: 2rem;
}

.input-group {
    position: relative;
}

.input-group label {
    display: block;
    font-weight: 500;
    color: #333;
    margin-bottom: 0.5rem;
}

.input-group textarea {
    width: 100%;
    min-height: 150px;
    padding: 1rem;
    border: 2px solid #e0e0e0;
    border-radius: 12px;
    font-family: inherit;
    font-size: 1rem;
    line-height: 1.6;
    resize: vertical;
    transition: all 0.3s ease;
    background: rgba(255, 255, 255, 0.9);
}

.input-group textarea:focus {
    outline: none;
    border-color: #B3CDE0;
    box-shadow: 0 0 0 3px rgba(179, 205, 224, 0.1);
}

.example-btn {
    position: absolute;
    top: 0;
    right: 0;
    background: #A2D9CE;
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 0 12px 0 12px;
    font-size: 0.8rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.example-btn:hover {
    background: #8cc7ba;
    transform: scale(1.05);
}

.analyze-section {
    text-align: center;
}

.analyze-btn {
    position: relative;
    background: linear-gradient(135deg, #B3CDE0 0%, #A2D9CE 100%);
    color: white;
    border: none;
    padding: 1rem 2.5rem;
    font-size: 1.1rem;
    font-weight: 500;
    border-radius: 50px;
    cursor: pointer;
    transition: all 0.3s ease;
    overflow: hidden;
    box-shadow: 0 8px 25px rgba(179, 205, 224, 0.3);
}

.analyze-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 35px rgba(179, 205, 224, 0.4);
}

.btn-ripple {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    transform: translate(-50%, -50%);
    transition: width 0.6s, height 0.6s;
}

.analyze-btn:active .btn-ripple {
    width: 300px;
    height: 300px;
}

/* 分析过程展示区 */
.analysis-section {
    position: relative;
    min-height: 400px;
    margin: 4rem 0;
    border-radius: 20px;
    overflow: hidden;
}

.analysis-background {
    position: relative;
    width: 100%;
    height: 400px;
    background: linear-gradient(135deg, rgba(179, 205, 224, 0.1) 0%, rgba(162, 217, 206, 0.1) 100%);
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
}

#networkCanvas {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0.6;
}

.analysis-content {
    position: relative;
    z-index: 2;
    text-align: center;
}

.analysis-text h3 {
    font-size: 1.5rem;
    color: #333;
    margin-bottom: 1rem;
    font-weight: 500;
}

.progress-dots {
    display: inline-flex;
    gap: 0.5rem;
}

.progress-dots span {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #B3CDE0;
    animation: pulse 1.5s infinite;
}

.progress-dots span:nth-child(2) {
    animation-delay: 0.3s;
}

.progress-dots span:nth-child(3) {
    animation-delay: 0.6s;
}

@keyframes pulse {
    0%, 100% { opacity: 0.3; transform: scale(1); }
    50% { opacity: 1; transform: scale(1.2); }
}

.view-report-btn {
    background: linear-gradient(135deg, #F5A623 0%, #ff8c42 100%);
    color: white;
    border: none;
    padding: 1rem 2rem;
    font-size: 1.1rem;
    font-weight: 500;
    border-radius: 50px;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-top: 2rem;
    box-shadow: 0 8px 25px rgba(245, 166, 35, 0.3);
}

.view-report-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 35px rgba(245, 166, 35, 0.4);
}

/* 结果展示区 */
.results-section {
    margin-top: 4rem;
}

.evidence-chain {
    background: rgba(255, 255, 255, 0.9);
    border-radius: 20px;
    padding: 2rem;
    margin-bottom: 2rem;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.evidence-chain h3 {
    font-size: 1.8rem;
    color: #333;
    margin-bottom: 2rem;
    text-align: center;
    font-weight: 500;
}

.evidence-timeline {
    position: relative;
    padding: 2rem 0;
}

.evidence-node {
    position: relative;
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    margin: 1rem 0;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    border-left: 4px solid #B3CDE0;
    transition: all 0.3s ease;
    opacity: 0;
    transform: translateX(-30px);
    animation: slideInLeft 0.6s ease forwards;
}

.evidence-node.gap {
    border-left-color: #ff6b6b;
    background: rgba(255, 107, 107, 0.05);
}

.evidence-node.gap::before {
    content: '⚠️';
    position: absolute;
    top: 1rem;
    right: 1rem;
    font-size: 1.2rem;
}

@keyframes slideInLeft {
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.evidence-node:nth-child(2) { animation-delay: 0.2s; }
.evidence-node:nth-child(3) { animation-delay: 0.4s; }
.evidence-node:nth-child(4) { animation-delay: 0.6s; }
.evidence-node:nth-child(5) { animation-delay: 0.8s; }

.evidence-node h4 {
    color: #333;
    margin-bottom: 0.5rem;
    font-weight: 500;
}

.evidence-node p {
    color: #666;
    line-height: 1.6;
    font-size: 0.9rem;
}

.full-report-btn {
    display: block;
    margin: 2rem auto 0;
    background: linear-gradient(135deg, #A2D9CE 0%, #B3CDE0 100%);
    color: white;
    border: none;
    padding: 1rem 2rem;
    font-size: 1.1rem;
    font-weight: 500;
    border-radius: 50px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 8px 25px rgba(162, 217, 206, 0.3);
}

.full-report-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 35px rgba(162, 217, 206, 0.4);
}

/* 综合报告 */
.full-report {
    background: rgba(255, 255, 255, 0.9);
    border-radius: 20px;
    padding: 2rem;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.report-tabs {
    display: flex;
    border-bottom: 2px solid #f0f0f0;
    margin-bottom: 2rem;
    gap: 1rem;
}

.tab-btn {
    background: none;
    border: none;
    padding: 1rem 1.5rem;
    font-size: 1rem;
    font-weight: 500;
    color: #666;
    cursor: pointer;
    border-radius: 8px 8px 0 0;
    transition: all 0.3s ease;
    position: relative;
}

.tab-btn.active {
    color: #B3CDE0;
    background: rgba(179, 205, 224, 0.1);
}

.tab-btn.active::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    right: 0;
    height: 2px;
    background: #B3CDE0;
}

.tab-panel {
    display: none;
    animation: fadeIn 0.5s ease;
}

.tab-panel.active {
    display: block;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* 报告内容样式 */
.insights-content, .simulation-content, .document-content {
    line-height: 1.8;
    color: #333;
}

.insights-content h4, .simulation-content h4 {
    color: #B3CDE0;
    font-size: 1.3rem;
    margin: 2rem 0 1rem 0;
    font-weight: 500;
    border-bottom: 2px solid #f0f0f0;
    padding-bottom: 0.5rem;
}

.statute-item, .case-item {
    background: rgba(179, 205, 224, 0.05);
    border-radius: 12px;
    padding: 1.5rem;
    margin: 1rem 0;
    border-left: 4px solid #B3CDE0;
}

.statute-item h5, .case-item h5 {
    color: #333;
    font-weight: 500;
    margin-bottom: 0.5rem;
    font-size: 1.1rem;
}

.statute-item p, .case-item p {
    color: #666;
    margin-bottom: 0.5rem;
}

.simulation-content ul {
    list-style: none;
    padding: 0;
}

.simulation-content li {
    background: rgba(255, 255, 255, 0.8);
    border-radius: 8px;
    padding: 1rem;
    margin: 0.5rem 0;
    border-left: 3px solid #A2D9CE;
    position: relative;
}

.simulation-content li::before {
    content: '•';
    color: #A2D9CE;
    font-size: 1.5rem;
    position: absolute;
    left: -1rem;
    top: 0.5rem;
}

/* 法律文书样式 */
.document-content {
    background: white;
    border-radius: 12px;
    padding: 2rem;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    font-family: 'Noto Sans SC', serif;
}

.document-header {
    text-align: center;
    margin-bottom: 2rem;
    border-bottom: 2px solid #f0f0f0;
    padding-bottom: 1rem;
}

.document-header h3 {
    font-size: 2rem;
    color: #333;
    font-weight: 700;
}

.party-info {
    background: rgba(245, 245, 245, 0.5);
    border-radius: 8px;
    padding: 1rem;
    margin: 1rem 0;
    line-height: 1.8;
}

.party-info p {
    margin: 0.3rem 0;
}

.case-cause {
    text-align: center;
    font-size: 1.1rem;
    font-weight: 500;
    margin: 2rem 0;
    padding: 1rem;
    background: rgba(179, 205, 224, 0.1);
    border-radius: 8px;
}

.claims {
    margin: 2rem 0;
}

.claims h4 {
    color: #333;
    font-size: 1.2rem;
    font-weight: 500;
    margin-bottom: 1rem;
}

.claims ol {
    padding-left: 2rem;
}

.claims li {
    margin: 0.5rem 0;
    line-height: 1.8;
}

.download-section {
    text-align: center;
    margin-top: 2rem;
    padding-top: 2rem;
    border-top: 2px solid #f0f0f0;
}

.download-btn {
    background: linear-gradient(135deg, #F5A623 0%, #ff8c42 100%);
    color: white;
    border: none;
    padding: 1rem 2rem;
    font-size: 1.1rem;
    font-weight: 500;
    border-radius: 50px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 8px 25px rgba(245, 166, 35, 0.3);
}

.download-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 35px rgba(245, 166, 35, 0.4);
}

/* 工具类 */
.hidden {
    display: none !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .features-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .input-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .logo-text {
        font-size: 3rem;
    }

    .welcome-container {
        padding: 1rem;
    }

    .report-tabs {
        flex-direction: column;
        gap: 0;
    }

    .tab-btn {
        border-radius: 8px;
        margin-bottom: 0.5rem;
    }

    .tab-btn.active::after {
        display: none;
    }
}
