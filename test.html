<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试页面</title>
</head>
<body>
    <h1>JavaScript测试</h1>
    <button onclick="testFunction()">点击测试</button>
    <div id="result"></div>
    
    <script>
        function testFunction() {
            document.getElementById('result').innerHTML = 'JavaScript正常工作！时间：' + new Date().toLocaleString();
            console.log('测试函数被调用');
        }
        
        console.log('测试页面加载完成');
    </script>
</body>
</html>
